# WebSocket并发连接问题分析与修复报告

## 问题概述

通过对WebSocket服务代码的全面分析，发现了几个可能导致并发连接问题的关键点，主要集中在 `lib/services/websocket_service/data/websocket_datasource.dart` 文件中。

## 发现的主要问题

### 1. Future.microtask 导致的竞态条件

**问题位置**: `_performConnect` 方法第139行
```dart
// 异步启动重连，不阻塞当前调用
Future.microtask(() => tryRefreshTokenAndReconnect());
```

**问题分析**:
- `Future.microtask` 会在当前事件循环的微任务队列中异步执行重连逻辑
- 如果在短时间内多次调用 `_performConnect` 失败，会创建多个微任务
- 虽然 `tryRefreshTokenAndReconnect` 内部有 `_activeReconnectTask` 保护，但微任务的异步特性可能导致时序问题

**修复方案**:
```dart
// 直接调用重连，避免Future.microtask可能导致的竞态条件
// 使用unawaited确保不阻塞当前调用
unawaited(tryRefreshTokenAndReconnect());
```

### 2. 连接状态检查的原子性不足

**问题位置**: `_establishConnection` 方法
```dart
// Close existing connection if any
await disconnect(closeCode: closeCode);

// 再次检查是否已经连接（可能在disconnect过程中其他任务已经连接）
if (_isConnected) {
  return Either.right(null);
}
```

**问题分析**:
- 在 `disconnect` 和连接状态检查之间存在时间窗口
- 多个并发任务可能同时通过 `_isConnected` 检查
- `_socket` 的创建和 `_isConnected` 的设置不是原子操作

**修复方案**:
添加连接状态锁 `_isConnecting`，确保连接操作的原子性：
```dart
// 添加连接状态锁，确保连接操作的原子性
bool _isConnecting = false;

// 在_establishConnection方法中：
// 检查是否正在连接中，避免并发连接
if (_isConnecting) {
  LogUtils.d('WebSocket正在连接中，跳过重复连接请求');
  return Either.right(null);
}

// 设置连接状态锁
_isConnecting = true;

try {
  // 连接逻辑...
} finally {
  // 无论成功还是失败，都要释放连接状态锁
  _isConnecting = false;
}
```

### 3. 重连任务清理时机问题

**问题位置**: `resetReconnectionState` 方法
```dart
_activeReconnectTask = null; // 清理活跃的重连任务
_activeConnectTask = null; // 清理活跃的连接任务
```

**问题分析**:
- 直接将任务设为 null，没有等待正在进行的任务完成
- 可能导致任务泄漏和并发控制失效

**修复方案**:
```dart
// 注意：不直接设置为null，而是让正在进行的任务自然完成
// 这样可以避免任务泄漏和竞态条件
// _activeReconnectTask 和 _activeConnectTask 会在各自的finally块中被清理

// 重置连接状态锁，允许新的连接尝试
_isConnecting = false;
```

## 潜在的并发连接场景

1. **初始连接失败 + 手动重连**: 用户手动触发连接的同时，失败的初始连接触发自动重连
2. **多个错误事件**: 网络错误和断开事件同时触发，导致多个重连任务
3. **应用生命周期变化**: 应用从后台恢复时，可能同时触发多个连接尝试
4. **Token刷新并发**: Token刷新成功和失败的回调可能并发执行

## 修复后的保护机制

### 1. 连接层面的保护
- `_activeConnectTask`: 防止多个连接任务同时执行
- `_isConnecting`: 防止连接过程中的竞态条件
- `_isConnected`: 基本的连接状态检查

### 2. 重连层面的保护
- `_activeReconnectTask`: 防止多个重连任务同时执行
- `_isDisposing`: 防止在dispose过程中触发重连

### 3. 资源清理的改进
- 在 `dispose` 方法中等待活跃任务完成
- 在异常情况下正确释放连接状态锁
- 使用 `finally` 块确保状态锁的可靠释放

## 验证建议

1. **单元测试**: 编写测试用例验证并发连接场景
2. **压力测试**: 模拟网络不稳定环境下的连接行为
3. **日志监控**: 增加详细日志，监控连接状态变化
4. **集成测试**: 在真实应用场景中验证修复效果

## 总结

通过添加连接状态锁、改进异步调用方式、优化资源清理机制，有效解决了WebSocket服务中可能导致并发连接的问题。修复后的代码能够确保在任何时候只维持一个有效的WebSocket连接，提高了系统的稳定性和可靠性。
