import 'package:flutter_audio_room/services/websocket_service/data/websocket_datasource_manager.dart';
import 'package:flutter_audio_room/shared/data/local/storage_service.dart';
import 'package:flutter_test/flutter_test.dart';

// Mock classes for testing
class MockStorageService implements StorageService {
  @override
  String get accessToken => 'test_access_token';

  @override
  String getString(String key) => 'UTC';

  // 实现其他必要的方法，返回默认值
  @override
  dynamic noSuchMethod(Invocation invocation) => super.noSuchMethod(invocation);
}

void main() {
  group('WebSocket Concurrency Tests', () {
    late MockStorageService mockStorageService;
    late WebSocketDataSourceManager manager;

    const testUrl = 'wss://test.example.com/ws';

    setUp(() {
      mockStorageService = MockStorageService();
      manager = WebSocketDataSourceManager(storageService: mockStorageService);
    });

    test(
        'should not create multiple data sources for same URL when called concurrently',
        () async {
      // 记录连接调用
      final connectionAttempts = <String>[];

      // 创建多个并发连接请求
      final futures = <Future>[];

      for (int i = 0; i < 5; i++) {
        futures.add(Future(() async {
          connectionAttempts.add('attempt_$i');

          // 模拟连接过程
          final dataSource = manager.getDataSource(testUrl);

          // 模拟连接延迟
          await Future.delayed(const Duration(milliseconds: 50));

          return dataSource;
        }));
      }

      // 等待所有任务完成
      final results = await Future.wait(futures);

      // 验证所有任务都执行了
      expect(results.length, equals(5));
      expect(connectionAttempts.length, equals(5));

      // 验证manager中只有一个数据源实例
      expect(manager.activeDataSourceCount, equals(1));

      // 验证所有返回的数据源都是同一个实例
      final firstDataSource = results[0];
      for (int i = 1; i < results.length; i++) {
        expect(identical(results[i], firstDataSource), isTrue,
            reason: 'All data sources for the same URL should be identical');
      }
    });

    test('should handle multiple URL connections independently', () async {
      const testUrl1 = 'wss://test1.example.com/ws';
      const testUrl2 = 'wss://test2.example.com/ws';
      const testUrl3 = 'wss://test3.example.com/ws';

      // 并发获取不同URL的数据源
      final futures = [
        Future(() => manager.getDataSource(testUrl1)),
        Future(() => manager.getDataSource(testUrl1)), // 重复URL
        Future(() => manager.getDataSource(testUrl2)),
        Future(() => manager.getDataSource(testUrl2)), // 重复URL
        Future(() => manager.getDataSource(testUrl3)),
      ];

      final dataSources = await Future.wait(futures);

      // 验证返回了5个数据源引用
      expect(dataSources.length, equals(5));

      // 验证相同URL返回相同实例
      expect(identical(dataSources[0], dataSources[1]), isTrue);
      expect(identical(dataSources[2], dataSources[3]), isTrue);

      // 验证不同URL返回不同实例
      expect(identical(dataSources[0], dataSources[2]), isFalse);
      expect(identical(dataSources[0], dataSources[4]), isFalse);
      expect(identical(dataSources[2], dataSources[4]), isFalse);

      // 验证manager管理了3个不同的数据源
      expect(manager.activeDataSourceCount, equals(3));
    });

    test('should handle rapid connect/disconnect cycles safely', () async {
      final operations = <Future>[];

      // 快速连接和断开循环
      for (int i = 0; i < 10; i++) {
        operations.add(Future(() async {
          // 获取数据源
          final dataSource = manager.getDataSource(testUrl);

          // 使用数据源的URL来验证它被正确创建
          expect(dataSource.url, equals(testUrl));

          // 模拟短暂使用
          await Future.delayed(const Duration(milliseconds: 10));

          // 如果是偶数索引，尝试移除数据源
          if (i % 2 == 0) {
            await manager.disposeTarget(testUrl);
          }

          return 'operation_$i';
        }));
      }

      // 等待所有操作完成
      final results = await Future.wait(operations);

      // 验证所有操作都完成了
      expect(results.length, equals(10));

      // 验证最终状态（可能有0个或1个数据源，取决于最后的操作）
      expect(manager.activeDataSourceCount, lessThanOrEqualTo(1));
    });

    test('should maintain data source consistency under concurrent access',
        () async {
      const numConcurrentAccess = 20;
      final accessResults = <String>[];
      final futures = <Future>[];

      // 并发访问同一个数据源
      for (int i = 0; i < numConcurrentAccess; i++) {
        futures.add(Future(() async {
          final dataSource = manager.getDataSource(testUrl);

          // 记录数据源的URL
          accessResults.add(dataSource.url);

          // 模拟一些操作
          await Future.delayed(const Duration(milliseconds: 5));

          return dataSource.url;
        }));
      }

      // 等待所有访问完成
      final results = await Future.wait(futures);

      // 验证所有访问都返回了相同的URL
      expect(results.length, equals(numConcurrentAccess));
      expect(results.every((url) => url == testUrl), isTrue);
      expect(accessResults.every((url) => url == testUrl), isTrue);

      // 验证只有一个数据源实例
      expect(manager.activeDataSourceCount, equals(1));
    });

    test('should handle concurrent connect calls to manager', () async {
      // 这个测试验证manager.connect方法的并发安全性
      final connectFutures = <Future>[];

      // 并发调用connect方法
      for (int i = 0; i < 5; i++) {
        connectFutures.add(Future(() async {
          // 模拟不同的延迟
          await Future.delayed(Duration(milliseconds: i * 10));

          // 调用connect方法
          return manager.connect(testUrl);
        }));
      }

      // 等待所有连接尝试完成
      final results = await Future.wait(connectFutures);

      // 验证所有连接尝试都返回了结果
      expect(results.length, equals(5));

      // 验证只有一个数据源被创建
      expect(manager.activeDataSourceCount, equals(1));

      // 注意：实际的连接可能会失败（因为这是测试环境），
      // 但重要的是验证不会创建多个数据源实例
    });
  });
}
