// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'friend_voice_call_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$friendVoiceCallHash() => r'7d6ec183a7f57db00a9a41b512ec2a439059068a';

/// See also [FriendVoiceCall].
@ProviderFor(FriendVoiceCall)
final friendVoiceCallProvider =
    AutoDisposeNotifierProvider<FriendVoiceCall, VoiceCallState>.internal(
  FriendVoiceCall.new,
  name: r'friendVoiceCallProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$friendVoiceCallHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FriendVoiceCall = AutoDisposeNotifier<VoiceCallState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
