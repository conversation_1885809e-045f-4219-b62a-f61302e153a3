// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'instant_call_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$instantCallHash() => r'ddf244c0217516eaba8c47941996fadded09656f';

/// See also [InstantCall].
@ProviderFor(InstantCall)
final instantCallProvider =
    AutoDisposeNotifierProvider<InstantCall, VoiceCallState>.internal(
  InstantCall.new,
  name: r'instantCallProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$instantCallHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$InstantCall = AutoDisposeNotifier<VoiceCallState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
