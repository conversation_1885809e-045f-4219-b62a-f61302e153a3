# WebSocket并发连接测试说明

## 测试目的

验证WebSocket DataSource Manager在管理连接时是否会存在多次连接到同一个WebSocket链接的可能，确保修复后的代码能够正确处理并发连接场景。

## 测试文件

- `test/services/websocket_service/websocket_concurrency_test.dart`

## 测试用例说明

### 1. 并发数据源创建测试
```dart
test('should not create multiple data sources for same URL when called concurrently')
```
**测试目标**: 验证对同一URL的并发请求只会创建一个数据源实例
**测试方法**: 
- 并发发起5个对同一URL的数据源获取请求
- 验证所有返回的数据源都是同一个实例
- 验证manager中只管理了一个数据源

### 2. 多URL独立连接测试
```dart
test('should handle multiple URL connections independently')
```
**测试目标**: 验证不同URL的连接是独立管理的
**测试方法**:
- 并发请求3个不同URL的数据源（包含重复请求）
- 验证相同URL返回相同实例，不同URL返回不同实例
- 验证manager正确管理了3个不同的数据源

### 3. 快速连接断开循环测试
```dart
test('should handle rapid connect/disconnect cycles safely')
```
**测试目标**: 验证快速的连接/断开操作不会导致状态不一致
**测试方法**:
- 执行10次快速的获取数据源和断开连接操作
- 验证所有操作都能正常完成
- 验证最终状态是干净的

### 4. 并发访问一致性测试
```dart
test('should maintain data source consistency under concurrent access')
```
**测试目标**: 验证高并发访问下数据源的一致性
**测试方法**:
- 20个并发任务同时访问同一个数据源
- 验证所有访问都返回相同的URL
- 验证只有一个数据源实例被创建

### 5. 并发连接调用测试
```dart
test('should handle concurrent connect calls to manager')
```
**测试目标**: 验证manager.connect方法的并发安全性
**测试方法**:
- 并发调用5次manager.connect方法
- 验证只创建一个数据源实例
- 验证所有连接尝试都有返回结果

## 运行测试

### 前提条件
确保项目依赖已安装：
```bash
flutter pub get
```

### 运行单个测试文件
```bash
flutter test test/services/websocket_service/websocket_concurrency_test.dart
```

### 运行所有测试
```bash
flutter test
```

### 运行特定测试用例
```bash
flutter test test/services/websocket_service/websocket_concurrency_test.dart --name "should not create multiple data sources"
```

## 预期结果

所有测试用例都应该通过，证明：

1. **无重复连接**: 对同一URL的并发请求不会创建多个WebSocket连接
2. **状态一致性**: 在并发访问下，数据源状态保持一致
3. **资源管理**: 连接和断开操作能够正确管理资源
4. **线程安全**: 并发操作不会导致竞态条件

## 测试覆盖的修复点

这些测试验证了我们在WebSocket服务中修复的关键问题：

1. **连接状态锁**: `_isConnecting` 标志防止并发连接
2. **任务管理**: `_activeConnectTask` 和 `_activeReconnectTask` 防止重复任务
3. **资源清理**: 正确的dispose和状态重置机制
4. **异步调用优化**: 避免Future.microtask导致的竞态条件

## 故障排除

如果测试失败，可能的原因：

1. **依赖问题**: 检查是否正确导入了所有必要的包
2. **Mock问题**: 确保MockStorageService正确实现了所有必要的方法
3. **时序问题**: 调整测试中的延迟时间
4. **环境问题**: 确保测试环境支持WebSocket相关的类

## 扩展测试

可以考虑添加的额外测试：

1. **网络错误模拟**: 测试网络错误情况下的重连行为
2. **内存泄漏检测**: 验证长时间运行下的内存使用
3. **性能测试**: 测试大量并发连接的性能表现
4. **集成测试**: 在真实WebSocket服务器上进行测试
